package cn.sh.ideal.controller;


import cn.sh.ideal.model.HxHistory;
import cn.sh.ideal.service.DataInputService;
import cn.sh.ideal.service.HxpageService;
import cn.sh.ideal.utils.PageUtils;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/hxpage")
public class HxPageController extends BaseController{
    private static final Logger logger = LoggerFactory.getLogger(HxPageController.class);

    @Autowired
    private HxpageService hxpageService;

    @Autowired
    private DataInputService dataInputService;
    /** 分页查询 */
    @GetMapping("getHistory")
    public String getHistory(Model model,Integer pageNum, Integer pageSize, HxHistory hxHistory){
        if (pageNum==null){
            pageNum = 1;
        }
        if (pageSize==null){
            pageSize = PageUtils.PAGE_SIZE;
        }
        JSONObject json = hxpageService.getHistoryService(hxHistory);
        JSONObject data = json.getJSONObject("DATA");
        data.put("pageNum", pageNum);
        model.addAttribute("pages",json.getJSONObject("DATA"));


        List<Long> beforePages = new ArrayList<>();
        List<Long> afterPages = new ArrayList<>();
        PageUtils.page(json.getJSONObject("DATA").getLong("PAGES"), pageNum,beforePages,afterPages);

        model.addAttribute("beforePages",beforePages);
        model.addAttribute("afterPages",afterPages);
        model.addAttribute("pageNum",pageNum);
        model.addAttribute("param",hxHistory);
        return "hxpage/history";
    }
    @GetMapping("searchWbinfo")
    public String searchWbinfo(Model model, String wbId){
        JSONObject json = hxpageService.searchWbinfo(wbId);
        model.addAttribute("data", json.getJSONObject("DATA"));
        return "hxpage/wbInfo";
    }
    @GetMapping("getCodeList")
    @ResponseBody
    public String getCodeList(){
        dataInputService.getCodeList();
        return "ok";
    }




}

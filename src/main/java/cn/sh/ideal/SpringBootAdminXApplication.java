package cn.sh.ideal;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;


/**
 * 启动类
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
public class SpringBootAdminXApplication   extends SpringBootServletInitializer {
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
		return builder.sources(SpringBootAdminXApplication.class);
	}
	public static void main(String[] args) {
		SpringApplication.run(SpringBootAdminXApplication.class, args);
	}

}



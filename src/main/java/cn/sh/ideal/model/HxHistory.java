package cn.sh.ideal.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

import static com.baomidou.mybatisplus.annotation.FieldFill.INSERT_UPDATE;

/**
 * <AUTHOR>
@Data
public class HxHistory implements Serializable {
    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    private String customerId;


    private String serviceTypeId;

    private String productId;
    private String prodSerialNo;
    private String order;
    private String pageno;
    private String pagecount;


    private String logoId;
    private String rowId;
    private String serviceCorpName;
    private String wbStatus;
    private String createdBy;

    private String wbId;


    private String faultDesc;

    private String enterTime;


    private static final long serialVersionUID = 1L;
}

package cn.sh.ideal.scheduler;


import cn.sh.ideal.service.DataInputService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class DataInputTask {



    @Autowired
    private DataInputService dataInputService;

    @Scheduled(cron = "${config.cron}")
    public String callTask() throws Exception {
        dataInputService.getCodeList();
        return "ok";
    }


}
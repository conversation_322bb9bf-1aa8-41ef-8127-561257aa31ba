package cn.sh.ideal.controller;

import com.alibaba.fastjson.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Scanner;
/**
 * ACTION基类
 * 
 * <AUTHOR>
 *
 */
public class BaseController {
	public final static String SUCCESS_CODE = "0";
	
	public final static String ERROR_CODE = "-1";
	
	public final static String SUCCESS_MSG = "success.";
	public final static String PARAM_RESULT_CODE="resultCode";

	public final static String PARAM_RESULT_MSG="resultMsg";
/*	private AuthorityUtil authorityUtil = AuthorityUtil.getInstance();
	
	@ModelAttribute
	public void checkAuthority() throws Exception {
		//验证License
		authorityUtil.checkLicense();
	}*/
	
	/**
	 * 流->字符串
	 * @param in
	 * @return
	 * @throws IOException
	 */
	public String streamToString(InputStream in)
			throws IOException {
		Scanner scanner = new Scanner(in, "utf-8");
		StringBuffer buffer = new StringBuffer();
		while(scanner.hasNextLine()) {
			buffer.append(scanner.nextLine());
		}
		scanner.close();
		return buffer.toString();
	}

	/**
	 * 返回成功JSON
	 * @return
	 */
	public JSONObject getSuccessJsonObj() {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put(PARAM_RESULT_CODE, SUCCESS_CODE);
		jsonObject.put(PARAM_RESULT_MSG, SUCCESS_MSG);
		return jsonObject;
	}
	
	public String getSuccessResult() {
		return getSuccessJsonObj().toJSONString();
	}
	
	public JSONObject getErrorJsonObj(String errmsg) {
		return getErrorJsonObj(ERROR_CODE, errmsg);
	}
	
	
	/**
	 * 返回失败JSON
	 * @param code
	 * @param errmsg
	 * @return
	 */
	public JSONObject getErrorJsonObj(String code, String errmsg) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put(PARAM_RESULT_CODE, code);
		jsonObject.put(PARAM_RESULT_MSG, errmsg);
		return jsonObject;
	}
	
	public String getErrorResult(String code, String errmsg) {
		return getErrorJsonObj(code, errmsg).toJSONString();
	}
	
	public String getErrorResult(String errmsg) {
		return getErrorResult(ERROR_CODE, errmsg);
	}
	public Map<String,Object> getSuccMap(Object data){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("resultCode", SUCCESS_CODE);
		map.put("result", SUCCESS_CODE);
		map.put("resultMsg", SUCCESS_MSG);
		map.put("data", data);
		
		return map;
	}
}

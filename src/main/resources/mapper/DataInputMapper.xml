<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sh.ideal.mapper.DataInputMapper">



    <insert id="insertAll" parameterType="java.util.List" useGeneratedKeys="false">
        insert into hcc_data_dictionaries_css (KINDVALUE,CODEVALUE,KINDNAME,CODENAME,LOAD_FLAG)
        <foreach item="item" index="index" collection="list" separator="union all">
            (
            SELECT
            #{item.kindvalue},
            #{item.codevalue},
            #{item.kindname},
            #{item.codename},
            '1'
            FROM DUAL
            )
        </foreach>
    </insert>


    <delete id="deleteByKindvalue">
        delete from HCC_DATA_DICTIONARIES_CSS WHERE KINDVALUE IN ('PRODBRAND','TS_CLASS','PRODTYPE')
    </delete>


</mapper>



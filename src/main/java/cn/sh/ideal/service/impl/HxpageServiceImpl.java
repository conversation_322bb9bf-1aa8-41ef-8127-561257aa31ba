package cn.sh.ideal.service.impl;

import cn.sh.ideal.model.HxHistory;
import cn.sh.ideal.service.HxpageService;
import cn.sh.ideal.utils.OKHttpClientBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Slf4j
@Service
public class HxpageServiceImpl implements HxpageService {

    @Value("${hx.userKey}")
    private String userKey;
    @Value("${hx.url}")
    private String url;
    @Override
    public JSONObject getHistoryService(HxHistory hxHistory){

        try {
            OkHttpClient client = OKHttpClientBuilder.buildOKHttpClient().build();

            MediaType mediaType = MediaType.parse("application/json");

            RequestBody body = RequestBody.create(mediaType, "{\"CUSTOMER_ID\":\""+hxHistory.getCustomerId()+"\",\"WB_ID\":\""+hxHistory.getWbId()+"\",\"SERVICE_TYPE_ID\":\""+hxHistory.getServiceTypeId()+"\",\"PRODUCT_ID\":\""+hxHistory.getProductId()+"\",\"LOGO_ID\":\""+hxHistory.getLogoId()+"\",\"ORDER\":\"\",\"PAGENO\":\"1\",\"PAGECOUNT\":\"10\"}");
            Request request = new Request.Builder()
                    .url(url+"/api/basic/wbInterface/wb/getHistoryService?user_key="+userKey)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            log.info(String.format("调用searchWbinfo结果  isSuccessful:[%s]", response.isSuccessful()));
            if (response.isSuccessful()) {

                String stryBody = response.body().string();
                try {
                    JSONObject respJson = JSON.parseObject(stryBody);
                    log.info(String.format("调用getCodeList_URL结果结果:[%s]", respJson));
                    return respJson;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();

        }
        return null;

    }
    @Override
    public JSONObject searchWbinfo(String wbId){

        try {
            OkHttpClient client = OKHttpClientBuilder.buildOKHttpClient().build();

            MediaType mediaType = MediaType.parse("application/json");

            RequestBody body = RequestBody.create(mediaType, "{\"WB_ID\":\""+wbId+"\"}");
            Request request = new Request.Builder()
                    .url(url+"/api/basic/wbInterface/wb/searchWbinfo?user_key="+userKey)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            log.info(String.format("调用searchWbinfo结果  isSuccessful:[%s]", response.isSuccessful()));
            if (response.isSuccessful()) {

                String stryBody = response.body().string();
                try {
                    JSONObject respJson = JSON.parseObject(stryBody);
                    log.info(String.format("调用getCodeList_URL结果结果:[%s]", respJson));
                    return respJson;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();

        }
        return null;

    }
}

spring:
  application:
    name: HXPAGE
  datasource:
    url: ******************************************
    #    url: ************************************************************************************
    username: hcc_app1
    password: q1w2e3r4
    druid:
      test-while-idle: true
      max-active: 20
      max-wait: 2000
      validationQuery: SELECT 1 FROM DUAL
      initial-size: 20
      login-timeout: 300000
      query-timeout: 300000
      remove-abandoned: true
      remove-abandoned-timeout: 180
      log-abandoned: true
      keep-alive: true
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/
  thymeleaf:
    cache: false
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss

#  redis:
#    host: 127.0.0.1
#    port: 29738
#    # 密码 没有则可以不填
#    password: ideal
#    # 如果使用的jedis 则将lettuce改成jedis即可
#    lettuce:
#      pool:
#        # 最大活跃链接数 默认8
#        max-active: 8
#        # 最大空闲连接数 默认8
#        max-idle: 8
#        # 最小空闲连接数 默认0
#        min-idle: 0
server:
  port: 20070
  servlet:
    context-path: /hxpages
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
management:
  health:
    redis:
      #    defaults:   ---也可以禁用，不推荐
      enabled: false
logging:
  config: classpath:log/logback-spring.xml
  file:
    name:
      path: nmyslog/nmys
hx:
  url: https://open-gw-test.hisense.com:443/rlcss
  userKey: bf062be7dc6045781084898ef3f574d7
config:
  cron: '0 0 1 * * ?'
package cn.sh.ideal.service.impl;

import cn.sh.ideal.mapper.DataInputMapper;
import cn.sh.ideal.model.Dictionaries;
import cn.sh.ideal.service.DataInputService;
import cn.sh.ideal.utils.OKHttpClientBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class DataInputServiceImpl implements DataInputService {
    @Value("${hx.userKey}")
    private String userKey;
    @Value("${hx.url}")
    private String url;
    @Autowired
    private DataInputMapper dataInputMapper;

    public  void getCodeList() {


        try {
            OkHttpClient client = OKHttpClientBuilder.buildOKHttpClient().build();

            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, "{}");

            Request request = new Request.Builder()
                    .url(url+"/api/basic/wbInterface/bs/getCodeList?user_key="+userKey)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            log.info(String.format("调用getCodeList结果  isSuccessful:[%s]", response.isSuccessful()));
            if (response.isSuccessful()) {

                String stryBody = response.body().string();
                try {
                    JSONObject respJson = JSON.parseObject(stryBody);
                    JSONArray data = respJson.getJSONArray("DATA");
                    List<Dictionaries> dictionariesList = new ArrayList<>();

                    /**
                     *  {
                     *             "KINDVALUE": "ywbk_Name",
                     *             "CODEVALUE": "34",
                     *             "KINDNAME": "业务板块",
                     *             "CODENAME": "约克中小",
                     *             "LOAD_FLAG": "0"
                     *         }
                     */
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject item= (JSONObject) data.get(i);
                        StringBuffer sb = new StringBuffer();
                        Dictionaries dictionarie = new Dictionaries();
                        dictionarie.setKindvalue(String.valueOf(item.get("KINDVALUE")));
                        dictionarie.setKindname(String.valueOf(item.get("KINDNAME")));
                        dictionarie.setCodename(String.valueOf(item.get("CODENAME")));
                        dictionarie.setCodevalue(String.valueOf(item.get("CODEVALUE")));
                        dictionariesList.add(dictionarie);

//                        sb.append("insert into hcc_data_dictionaries_css (KINDVALUE,CODEVALUE,KINDNAME,CODENAME,LOAD_FLAG) values ('");
//                        sb.append(item.get("KINDVALUE"));
//                        sb.append("','");
//                        sb.append(item.get("CODEVALUE"));
//                        sb.append("','");
//                        sb.append(item.get("KINDNAME"));
//                        sb.append("','");
//                        sb.append(item.get("CODENAME"));
//                        sb.append("','");
//                        sb.append(item.get("LOAD_FLAG"));
//                        sb.append("');");
//
//                        System.out.println(sb.toString());
                    }
                    if(dictionariesList.size()>0) {
                        dataInputMapper.deleteByKindvalue();
                        dataInputMapper.insertAll(dictionariesList);
                    }
                    log.info(String.format("调用getCodeList_URL结果结果:[%s]", respJson));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();

        }
    }

}

<!DOCTYPE html>
<html class="x-admin-sm" xmlns:th="http://www.thymeleaf.org">
<head>
	<meta charset="UTF-8"/>
	<title>澳門海關自動語音通話服務</title>
	<meta name="renderer" content="webkit|ie-comp|ie-stand"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta http-equiv="Cache-Control" content="no-siteapp"/>

    <link rel="stylesheet" th:href="@{/css/font.css}"/>
    <link rel="stylesheet" th:href="@{/css/login.css}"/>
	<link rel="stylesheet" th:href="@{/css/xadmin.css}"/>

    <script type="text/javascript" th:src="@{/js/jquery.min.3.2.1.js}"></script>
    <script th:src="@{/lib/layui/layui.js}" charset="utf-8" type="text/javascript"></script>
    <script th:src="@{/lib/sliderVerify/sliderVerify.js}" type="text/javascript" charset="utf-8"></script>


    <!--[if lt IE 9]>
      <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
      <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body class="login-bg">
    
    <div class="login layui-anim layui-anim-up">
        <div class="message">澳門海關自動語音通話服務</div>
        <div id="darkbannerwrap"></div>
        
        <form method="post" class="layui-form" th:action="@{/login}">
            <input name="username" placeholder="用户名" type="text" lay-verify="required" class="layui-input"/>
            <hr class="hr15"/>
            <input name="password" lay-verify="required" placeholder="密码" type="password" class="layui-input"/>
            <!--<hr class="hr15"/>
            <div class="layui-form-item">
                <div id="slider"></div>
            </div>-->
            <hr class="hr15"/>
            <input value="登录" lay-submit lay-filter="login" style="width:100%;" type="submit"/>
            <hr class="hr20"/>
        </form>
    </div>
    <script type="text/javascript">
        layui.use(['sliderVerify', 'jquery', 'form'], function() {
            var sliderVerify = layui.sliderVerify,
                form = layui.form;
            var slider = sliderVerify.render({
                elem: '#slider',
                onOk: function(){//当验证通过回调
                    layer.msg("滑块验证通过");
                }
            })
            //监听提交
            form.on('submit(formDemo)', function(data) {
                return true;
                /*if(slider.isOk()){
                    layer.msg(JSON.stringify(data.field));
                    return true;
                }else{
                    layer.msg("请先通过滑块验证");
                    return false;
                }*/
            });
        })
    </script>
</body>

</html>
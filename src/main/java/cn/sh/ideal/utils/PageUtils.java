package cn.sh.ideal.utils;

import java.util.List;

public final class PageUtils {

    public static final int PAGE_SIZE = 10;

    public static void page(long total, long current, List<Long> beforePages, List<Long> afterPages){
        if (total<=7){
            if (current==1){
                for (long i = 2; i <=total ; i++) {
                    afterPages.add(i);
                }
            }else if (current==total){
                for (long i = 1; i <=total-1 ; i++) {
                    beforePages.add(i);
                }
            }else{
                for (long i = 1; i <=current-1 ; i++) {
                    beforePages.add(i);
                }
                for (long i = current+1; i <=total ; i++) {
                    afterPages.add(i);
                }
            }
        }else{
            if (current==1){
                for (long i = total-2; i <=total ; i++) {
                    afterPages.add(i);
                }
            }else if (current==total){
                for (long i = 1; i <=3 ; i++) {
                    beforePages.add(i);
                }
            }else{
                if (current<=4){
                    for (long i = 1; i <=current-1 ; i++) {
                        beforePages.add(i);
                    }
                }
                if (current>4){
                    for (long i = 1; i <=3 ; i++) {
                        beforePages.add(i);
                    }
                }
                if (current>=total-3){
                    for (long i = current+1; i <=total ; i++) {
                        afterPages.add(i);
                    }
                }
                if (current<total-3){
                    for (long i = total-2; i <=total ; i++) {
                        afterPages.add(i);
                    }
                }
            }
        }

    }

}

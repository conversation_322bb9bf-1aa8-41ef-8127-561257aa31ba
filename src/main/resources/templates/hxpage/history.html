<!DOCTYPE html>
<html class="x-admin-sm" xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity5">

<head>
    <meta charset="UTF-8">
    <title>综合查询</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

    <link rel="stylesheet" th:href="@{/css/font.css}"/>
    <link rel="stylesheet" th:href="@{/css/xadmin.css}"/>

    <script th:src="@{/lib/layui/layui.js}" charset="utf-8"></script>
    <script type="text/javascript" th:src="@{/js/xadmin.js}"></script>

    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
<div class="x-nav">
            <span class="layui-breadcrumb">
                <a>
                    <cite>综合查询</cite></a>
            </span>
    <a class="layui-btn layui-btn-small" style="line-height:1.6em;margin-top:3px;float:right"
       onclick="location.reload()" title="刷新">
        <i class="layui-icon layui-icon-refresh" style="line-height:30px"></i>
    </a>
</div>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body ">
                    <form id="searchForm" class="layui-form layui-col-space5">

<!--                        【信息编号】-->
<!--                        【区号】-->
<!--                        【电话】-->
<!--                        【地址】-->
<!--                        【登记时间】-->
<!--                        【登记人】-->
<!--                        【品牌】-->
                        <input type="hidden" name="pageNum" id="current" th:value="${pages.get('pageNum')}"/>
<!--                        <input type="hidden" name="pageSize" th:value="${pages.getSize()}"/>-->
                        <div class="layui-input-inline layui-show-xs-block">
                            <input type="text" name="customerId" placeholder="请输入用户编号" th:value="${param.customerId}"
                                   autocomplete="off" class="layui-input"></div>
                        <div class="layui-input-inline layui-show-xs-block">
                        <input type="text" name="wbId" placeholder="请输入工单编号" th:value="${param.wbId}"
                                   autocomplete="off" class="layui-input"></div>
                        <div class="layui-input-inline layui-show-xs-block">
                            <select class="layui-select" name="serviceTypeId" autocomplete="off" >
                                <option value="">-请选择-</option>
                                <option value="2"  th:field="${param.serviceTypeId}">>安装_工装</option>
                                <option value="10" th:field="${param.serviceTypeId}">管理（老板）</option>
                                <option value="9" th:field="${param.serviceTypeId}">技师认定</option>
                                <option value="8" th:field="${param.serviceTypeId}">运营管理</option>
                                <option value="7" th:field="${param.serviceTypeId}">结算管理</option>
                                <option value="6" th:field="${param.serviceTypeId}">配件管理</option>
                                <option value="5" th:field="${param.serviceTypeId}">信息管理</option>
                                <option value="4" th:field="${param.serviceTypeId}">维保更新</option>
                                <option value="3" th:field="${param.serviceTypeId}">增值服务</option>
                                <option value="1" th:field="${param.serviceTypeId}">安装_家装</option>
                                <option value="ZX" th:field="${param.serviceTypeId}">售后咨询</option>
                                <option value="ZA" th:field="${param.serviceTypeId}">售前咨询</option>
                                <option value="YY" th:field="${param.serviceTypeId}">预约</option>
                                <option value="AZ" th:field="${param.serviceTypeId}">安装</option>
                                <option value="BY" th:field="${param.serviceTypeId}">表扬</option>
                                <option value="DY" th:field="${param.serviceTypeId}">产品升级</option>
                                <option value="HJ" th:field="${param.serviceTypeId}">换机</option>
                                <option value="JD" th:field="${param.serviceTypeId}">鉴定</option>
                                <option value="SH" th:field="${param.serviceTypeId}">运输损坏</option>
                                <option value="TJ" th:field="${param.serviceTypeId}">退机</option>
                                <option value="TS" th:field="${param.serviceTypeId}">调试</option>
                                <option value="TX" th:field="${param.serviceTypeId}">投诉</option>
                                <option value="WX" th:field="${param.serviceTypeId}">维修</option>
                            </select>
                        </div>
                        <div class="layui-input-inline layui-show-xs-block">
                            <select class="layui-select" name="productId" autocomplete="off">
                                <option value="">-请选择-</option>
                                <option value="284389700857012224" th:field="${param.productId}">冷水机组</option>
                                <option value="284389574348414976" th:field="${param.productId}">单元机</option>
                                <option value="1234" th:field="${param.productId}">多联机</option>
                            </select>
                        </div>
                        <div class="layui-input-inline layui-show-xs-block">
                            <select class="layui-select" name="logoId" autocomplete="off">
                                <option value="">请选择</option>
                                <option value="238458455614857216" th:field="${param.logoId}">日立</option>
                                <option value="238350500911423488" th:field="${param.logoId}">海信</option>
                                <option value="238352970173685760" th:field="${param.logoId}">约克</option>
                            </select>
                        </div>
                        <div class="layui-input-inline layui-show-xs-block">
                            <button class="layui-btn" onclick="search()" >
                                <i class="layui-icon">&#xe615;</i></button>
                        </div>
                    </form>
                </div>
                <div class="layui-card-body " style="overflow-x: scroll">
                    <table class="layui-table layui-form">
                        <colgroup>
                            <col>
                            <col >
                            <col >
                            <col>
                            <col>
                            <col>
                            <col >
                            <col >
                            <col>
                            <col>
                            <col>
                            <col >
                            <col >
                            <col>
                            <col>
                            <col>
                            <col >
                            <col >
                            <col >
                            <col>
                            <col>
                            <col>
                            <col >
                            <col >
                            <col>
                            <col>
                            <col>
                            <col >
                            <col >
                            <col>
                            <col>
                            <col>
                            <col >
                            <col >
                            <col>
                            <col>
                            <col >
                            <col >
                            <col>
                            <col>
                            <col>
                            <col>
                        </colgroup>
                        <thead>
                        <tr>
                            <th>信息ID</th>
                            <th>要求服务类型ID</th>
                            <th>登记时间</th>
                            <th>要求服务时间</th>
                            <th>预约时间描述</th>
                            <th>用户反映问题描述</th>
                            <th>创建人，保存名称，用户姓名</th>
                            <th>服务商结单时间</th>
                            <th>工单状态</th>
                            <th>服务商ID</th>
                            <th>服务商名称</th>
                            <th>未完成原因ID</th>
                            <th>未完成原因名称</th>
                            <th>产品大类ID</th>
                            <th>产品型号ID(外机）</th>
                            <th>产品型号名称</th>
                            <th>用户产品ID</th>
                            <th>品牌ID</th>
                            <th>项目编码</th>
                            <th>工单类型</th>
                            <th>保内保外标志</th>
                            <th>内机型号ID</th>
                            <th>内机型号名称</th>
                            <th>故障机</th>
                            <th>工程ID</th>
                            <th>工程名称</th>
                            <th>工程状态</th>
                            <th>工程地址</th>
                            <th>工程类型</th>
                            <th>安装商名称</th>
                            <th>保修开始时间</th>
                            <th>调试完成时间</th>
                            <th>开工时间</th>
                            <th>保修结束时间</th>
                            <th>经销商名称</th>
                            <th>是否新品</th>
                            <th>生产日期</th>
                            <th>产地ID</th>
                            <th>产地名称</th>
                            <th>生产方式ID</th>
                            <th>生产方式名称</th>
                            <th>故障机制造编号</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr th:each="item,itemStat : ${pages.get('LIST')}">
                            <td th:text="${item.get('ROW_ID')}"></td>
                            <td th:switch="${item.get('SERVICE_TYPE_ID')}">
                                <span th:case="2">安装_工装</span>
                                <span th:case="10">管理（老板）</span>
                                <span th:case="9">技师认定</span>
                                <span th:case="8">运营管理</span>
                                <span th:case="7">结算管理</span>
                                <span th:case="6">配件管理</span>
                                <span th:case="5">信息管理</span>
                                <span th:case="4">维保更新</span>
                                <span th:case="3">增值服务</span>
                                <span th:case="1">安装_家装</span>
                                <span th:case="ZX">售后咨询</span>
                                <span th:case="ZA">售前咨询</span>
                                <span th:case="YY">预约</span>
                                <span th:case="AZ">安装</span>
                                <span th:case="BY">表扬</span>
                                <span th:case="DY">产品升级</span>
                                <span th:case="HJ">换机</span>
                                <span th:case="JD">鉴定</span>
                                <span th:case="SH">运输损坏</span>
                                <span th:case="TJ">退机</span>
                                <span th:case="TS">调试</span>
                                <span th:case="TX">投诉</span>
                                <span th:case="WX">维修</span>
                                <span th:case="*"></span>
                            </td>
                            <td th:text="${item.get('ENTER_TIME')}"></td>
                            <td th:text="${item.get('REQUIRE_SERVICE_DATE')}"></td>
                            <td th:text="${item.get('REQUIRE_DESC')}"></td>
                            <td th:text="${item.get('FAULT_DESC')}"></td>
                            <td th:text="${item.get('CREATED_BY')}"></td>
                            <td th:text="${item.get('SERVICE_CLOSE_TIME')}"></td>
                            <td th:text="${item.get('WB_STATUS')}"></td>
                            <td th:text="${item.get('SERVICE_CORP_ID')}"></td>
                            <td th:text="${item.get('SERVICE_CORP_NAME')}"></td>
                            <td th:text="${item.get('UNFINISHED_REASON')}"></td>
                            <td th:text="${item.get('UNFINISHED_REASON_DESC')}"></td>
                            <td th:switch="${item.get('PRODUCT_ID')}">
                                <span th:case="284389700857012224">冷水机组</span>
                                <span th:case="284389574348414976">单元机</span>
                                <span th:case="1234">多联机</span>
                                <span th:case="*"></span>
                            </td>
                            <td th:text="${item.get('MODEL_ID')}"></td>
                            <td th:text="${item.get('MODEL_NAME')}"></td>
                            <td th:text="${item.get('CUSTOMER_PRODUCT_ID')}"></td>
                            <td th:switch="${item.get('LOGO_ID')}">
                                <span th:case="238458455614857216">日立</span>
                                <span th:case="238350500911423488">海信</span>
                                <span th:case="238352970173685760">约克</span>
                                <span th:case="*"></span>
                            </td>
<!--                            <td th:if="${item.get('LOGO_ID')} == 'HT'">日立</td>-->
<!--                            <td th:if="${item.get('LOGO_ID')} == 'HX'">海信</td>-->
<!--                            <td th:if="${item.get('LOGO_ID')} == 'YK'">约克</td>-->
                            <td th:text="${item.get('PROJECT_CODE')}"></td>
                            <td th:text="${item.get('WB_TYPE')}"></td>
                            <td th:text="${item.get('IF_WARRANT')}"></td>
                            <td th:text="${item.get('INNER_MODEL_ID')}"></td>
                            <td th:text="${item.get('INNER_MODEL_NAME')}"></td>
                            <td th:text="${item.get('FAULT_MACHINE')}"></td>
                            <td th:text="${item.get('PROJECT_ID')}"></td>
                            <td th:text="${item.get('PROJECT_NAME')}"></td>
                            <td th:text="${item.get('PROJECT_STATUS')}"></td>
                            <td th:text="${item.get('PROJECT_ADDRESS')}"></td>
                            <td th:text="${item.get('PROJECT_TYPE')}"></td>
                            <td th:text="${item.get('INSTALLER_NAME')}"></td>
                            <td th:text="${item.get('WARRANTY_BEGIN_DATE')}"></td>
                            <td th:text="${item.get('DEBUGGING_DATE')}"></td>
                            <td th:text="${item.get('BEGIN_DATE')}"></td>
                            <td th:text="${item.get('WARRANTY_END_DATE')}"></td>
                            <td th:text="${item.get('DEALER_ID')}"></td>
                            <td th:text="${item.get('IF_NEW')}"></td>
                            <td th:text="${item.get('PRODUCT_DATE')}"></td>
                            <td th:text="${item.get('PRODUCING_AREA_ID')}"></td>
                            <td th:text="${item.get('PRODUCING_AREA')}"></td>
                            <td th:text="${item.get('PRODUCT_MODE_ID')}"></td>
                            <td th:text="${item.get('PRODUCT_MODE')}"></td>
                            <td th:text="${item.get('FAULT_MAC_CODE')}"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="layui-card-body ">
                    <div class="page">
                        <div th:if="${pages.get('PAGES')<=7}">
                            <a class="prev" href="javascript:void(0)"
                               th:onclick="'javascript:toPage('+${(pages.getInteger('pageNum')-1)<=0?1:(pages.getInteger('pageNum')-1)}+')'">上一页</a>
                            <a href="javascript:void(0)" th:each="beforeNo:${beforePages}" th:text="${beforeNo}"
                               class="num" th:onclick="'javascript:toPage('+${beforeNo}+')'"></a>
                            <span class="current" th:text="${pages.get('pageNum')}"></span>
                            <a href="javascript:void(0)" th:each="afterNo:${afterPages}" th:text="${afterNo}"
                               class="num" th:onclick="'javascript:toPage('+${afterNo}+')'"></a>
                            <a href="javascript:void(0)" class="next"
                               th:onclick="'javascript:toPage('+${(pages.getInteger('pageNum')+1)>=pages.getInteger('PAGES')?pages.getInteger('PAGES'):(pages.getInteger('pageNum')+1)}+')'">下一页</a>
                        </div>
                        <div th:if="${pages.get('PAGES')>7}">
                            <a class="prev" href="javascript:void(0)"
                               th:onclick="'javascript:toPage('+${(pages.get('pageNum')-1)<=0?1:(pages.get('pageNum')-1)}+')'">上一页</a>
                            <a href="javascript:void(0)" th:each="beforeNo:${beforePages}" th:text="${beforeNo}"
                               class="num" th:onclick="'javascript:toPage('+${beforeNo}+')'"></a>
                            <span th:if="${pages.get('pageNum')>4}">...</span>
                            <span class="current" th:text="${pages.get('pageNum')}"></span>
                            <span th:if="${pages.get('pageNum')<(pages.get('PAGES')-3)}">...</span>
                            <a href="javascript:void(0)" th:each="afterNo:${afterPages}" th:text="${afterNo}"
                               class="num" th:onclick="'javascript:toPage('+${afterNo}+')'"></a>
                            <a href="javascript:void(0)" class="next"
                               th:onclick="'javascript:toPage('+${(pages.get('pageNum')+1)>=pages.get('PAGES')?pages.get('PAGES'):(pages.get('pageNum')+1)}+')'">下一页</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script>layui.use(['laydate', 'form'],
    function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            elem: '#start' //指定元素
        });

        //执行一个laydate实例
        laydate.render({
            elem: '#end' //指定元素
        });
    });

/*用户-停用*/
function member_stop(obj, id) {
    layer.confirm('确认要停用吗？',
        function (index) {

            if ($(obj).attr('title') == '启用') {

                //发异步把用户状态进行更改
                $(obj).attr('title', '停用');
                $(obj).find('i').html('&#xe62f;');

                $(obj).parents("tr").find(".td-status").find('span').addClass('layui-btn-disabled').html('已停用');
                layer.msg('已停用!', {
                    icon: 5,
                    time: 1000
                });

            } else {
                $(obj).attr('title', '启用');
                $(obj).find('i').html('&#xe601;');

                $(obj).parents("tr").find(".td-status").find('span').removeClass('layui-btn-disabled').html('已启用');
                layer.msg('已启用!', {
                    icon: 5,
                    time: 1000
                });
            }

        });
}
function search(){
    $("#current").val(1);
    $("#searchForm").submit();
}
function toPage(num) {
    $("#current").val(num);
    $("#searchForm").submit();
}


function serviceTypeIdDict(value){
    if(value){
        var type={"2":"安装_工装","10":"管理（老板）",
            "9":"技师认定",
            "8":"运营管理",
            "7":"结算管理",
            "6":"配件管理",
            "5":"信息管理",
            "4":"维保更新",
            "3":"增值服务",
            "1":"安装_家装",
            "ZX":"售后咨询",
            "ZA":"售前咨询",
            "YY":"预约",
            "AZ":"安装",
            "BY":"表扬",
            "DY":"产品升级",
            "HJ":"换机",
            "JD":"鉴定",
            "SH":"运输损坏","TJ":"退机","TS":"调试","TX":"投诉","WX":"维修"};
        return type[value]||value;
    }else{
        return '';
    }
}
function productIdDict(value){
    var type={"284389700857012224":"冷水机组","284389574348414976":"单元机","1234":"多联机"};
    return type[value]||value;
}
function logoIdDict(value){
    var type={"HT":"日立","HX":"海信","YK":"日立"}
    return type[value]||value;
}

</script>

</html>

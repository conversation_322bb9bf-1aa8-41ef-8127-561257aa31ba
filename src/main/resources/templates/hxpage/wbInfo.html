<!DOCTYPE html>
<html class="x-admin-sm" xmlns:th="http://www.thymeleaf.org"
      xmlns:sec="http://www.thymeleaf.org/thymeleaf-extras-springsecurity5">

<head>
    <meta charset="UTF-8">
    <title>查看页面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

    <link rel="stylesheet" th:href="@{/css/font.css}"/>
    <link rel="stylesheet" th:href="@{/css/xadmin.css}"/>

    <script th:src="@{/lib/layui/layui.js}" charset="utf-8"></script>
    <script type="text/javascript" th:src="@{/js/xadmin.js}"></script>

    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
<div class="x-nav">
            <span class="layui-breadcrumb">
                <a>
                    <cite>详细信息</cite></a>
            </span>
    <a class="layui-btn layui-btn-small" style="line-height:1.6em;margin-top:3px;float:right"
       onclick="location.reload()" title="刷新">
        <i class="layui-icon layui-icon-refresh" style="line-height:30px"></i>
    </a>
</div>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">

<!--                <div class="layui-card-body ">-->
<!--                    <table class="layui-table layui-form">-->
<!--                        <colgroup>-->
<!--                            <col>-->
<!--                            <col>-->
<!--                            <col>-->
<!--                            <col>-->
<!--                            <col>-->
<!--                            <col width="200">-->
<!--                        </colgroup>-->
<!--                        <thead>-->
<!--                        <tr>-->
<!--                            <th>文件标题</th>-->
<!--                            <th>文案语言</th>-->
<!--                            <th>内容</th>-->
<!--                            <th>创建人</th>-->
<!--                            <th>创建时间</th>-->
<!--                        </tr>-->
<!--                        </thead>-->
<!--                        <tbody>-->
<!--                        <tr th:each="item,itemStat : ${pages.getRecords()}">-->
<!--                            <td th:text="${item.id}" style="display: none;"></td>-->
<!--                            <td th:text="${item.copywritingName}"></td>-->
<!--                            <td th:if="${item.language} == 1">中文</td>-->
<!--                            <td th:if="${item.language} == 2">English</td>-->
<!--                            <td th:if="${item.language} == 3">Português</td>-->
<!--                            <td th:text="${item.content}"></td>-->
<!--                            <td th:text="${item.createUser}"></td>-->
<!--                        </tr>-->
<!--                        </tbody>-->
<!--                    </table>-->
<!--                </div>-->
                <div class="layui-card-body ">
                </div>
                <div class="layui-card">
                    <div class="layui-card-header">用户档案信息</div>
                    <div class="layui-card-body ">
                        <table class="layui-table"  th:each="customerInfo:${data.get('CUSTOMER_INFO')}">
                            <tbody>

                            <tr>
                                <th>用户信息编号:</th>
                                <td th:text="${customerInfo.get('ROW_ID')}"></td>
                                <th>登记部门:</th>
                                <td th:text="${customerInfo.get('REGISTER_DEPT')}"></td>
                                <th>所属呼叫中心:</th>
                                <td th:text="${customerInfo.get('BELONG_CALLCENTER')}"></td>
                                <th>用户类型ID:</th>
                                <td th:text="${customerInfo.get('CUSTOMER_TYPE')}"></td>
                            </tr>
                            <tr>
                                <th>用户级别:</th>
                                <td th:text="${customerInfo.get('CUSTOMER_LEVEL')}"></td>
                                <th>登录时间:</th>
                                <td th:text="${customerInfo.get('REGISTER_DATE')}"></td>
                                <th>邮件:</th>
                                <td th:text="${customerInfo.get('EMAIL')}"></td>
                                <th>家庭电话:</th>
                                <td th:text="${customerInfo.get('HOME_TELEPHONE')}"></td>
                            </tr>                 <tr>
                                <th>其他电话:</th>
                                <td th:text="${customerInfo.get('OFFICE_TELEPHONE')}"></td>
                                <th>手机:</th>
                                <td th:text="${customerInfo.get('MOBILE_PHONE')}"></td>
                                <th>用户类型名称:</th>
                                <td th:text="${customerInfo.get('CUSTOMER_TYPE_VALUE')}"></td>
                                <th>行政区县ID:</th>
                                <td th:text="${customerInfo.get('REGOIN_ID')}"></td>
                            </tr>
                            <tr>
                                <th>详细地址:</th>
                                <td th:text="${customerInfo.get('ADDRESS')}"></td>
                                <th>道路ID:</th>
                                <td th:text="${customerInfo.get('ROAD_ID')}"></td>
                                <th>区号:</th>
                                <td th:text="${customerInfo.get('AREA_CODE1')}"></td>
                                <th>登记人:</th>
                                <td th:text="${customerInfo.get('REGISTER_BY')}"></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="layui-card">
                    <div class="layui-card-header">服务请求信息</div>
                    <div class="layui-card-body ">
                        <table class="layui-table"  th:each="serviceInfo:${data.get('SERVICE_INFO')}">
                            <tbody>
                            <tr>
                                <th>业务类别ID:</th>
                                <td th:text="${serviceInfo.get('BUSINESS_TYPE')}"></td>
                                <th>品牌名称:</th>
                                <td th:text="${serviceInfo.get('LOGO_NAME')}"></td>
                                <th>产品大类名称:</th>
                                <td th:text="${serviceInfo.get('PRODUCT_NAME')}"></td>
                                <th>业务类别:</th>
                                <td th:text="${serviceInfo.get('BUSINESS_TYPE')}"></td>
                            </tr>
                            <tr>
                                <th>要求服务类型:</th>
                                <td th:text="${serviceInfo.get('SERVICE_MODE')}"></td>
                                <th>要求服务方式:</th>
                                <td th:text="${serviceInfo.get('SERVICE_MODE')}"></td>
                                <th>多次来电标识:</th>
                                <td th:text="${serviceInfo.get('MULTIPLE_CALLS')}"></td>
                                <th>是否风险工单:</th>
                                <td th:text="${serviceInfo.get('IF_RISK')}"></td>
                            </tr>
                            <tr>
                                <th>关联工单号:</th>
                                <td th:text="${serviceInfo.get('APPEAL_WO_ID')}"></td>
                                <th>工单类型:</th>
                                <td th:text="${serviceInfo.get('WB_TYPE')}"></td>
                                <th>工单状态:</th>
                                <td th:text="${serviceInfo.get('WB_STATUS')}"></td>
                                <th>预约开始时间:</th>
                                <td th:text="${serviceInfo.get('NEW_BOOKING_START_DATE')}"></td>
                            </tr>
                            <tr>
                                <th>预约时间范围:</th>
                                <td th:text="${serviceInfo.get('NEW_BOOKING_RANGE')}"></td>
                                <th>要求服务时间:</th>
                                <td th:text="${serviceInfo.get('NEW_BOOKING_TIME')}"></td>
                                <th>服务产品ID:</th>
                                <td th:text="${serviceInfo.get('SERVICE_PRODUCT_ID')}"></td>
                                <th>服务产品编码:</th>
                                <td th:text="${serviceInfo.get('SERVICE_PRODUCT_CODE')}"></td>
                            </tr>
                            <tr>
                                <th>服务产品名称:</th>
                                <td th:text="${serviceInfo.get('SERVICE_PRODUCT_NAME')}"></td>
                                <th>服务产品数量:</th>
                                <td th:text="${serviceInfo.get('SERVICE_PRODUCT_QTY')}"></td>
                                <th>产品分类编码:</th>
                                <td th:text="${serviceInfo.get('PROD_TYPE_CODE')}"></td>
                                <th>工单业务来源:</th>
                                <td th:text="${serviceInfo.get('WB_SOURCE_TYPE')}"></td>
                            </tr>
                            <tr>
                                <th>业务来源名称:</th>
                                <td th:text="${serviceInfo.get('WB_SOURCE_NAME')}"></td>
                                <th>来源单据编码:</th>
                                <td th:text="${serviceInfo.get('WB_SOURCE_CODE')}"></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="layui-card">
                    <div class="layui-card-header">工单处理信息</div>
                    <div class="layui-card-body ">
                        <table class="layui-table"  th:each="wbInfo:${data.get('WB_INFO')}">
                            <tbody>
                            <tr>
                                <th>派单人名称（分公司）:</th>
                                <td th:text="${wbInfo.get('ASSIGNED_NAME')}"></td>
                                <th>自动派单失败原因:</th>
                                <td th:text="${wbInfo.get('ASSIGN_RESULT_DESC')}"></td>
                                <th>服务商ID:</th>
                                <td th:text="${wbInfo.get('SERVICE_CORP_ID')}"></td>
                                <th>服务商编码:</th>
                                <td th:text="${wbInfo.get('SERVICE_CORP_CODE')}"></td>
                            </tr>
                            <tr>
                                <th>服务商名称:</th>
                                <td th:text="${wbInfo.get('SERVICE_CORP_NAME')}"></td>
                                <th>服务商接收时间:</th>
                                <td th:text="${wbInfo.get('SERVER_RJ_DATE')}"></td>
                                <th>服务商退单原因:</th>
                                <td th:text="${wbInfo.get('SERVER_REFUSE_REASON')}"></td>
                                <th>服务商派单人名称:</th>
                                <td th:text="${wbInfo.get('SERVICE_ASSIGNED_NAME')}"></td>
                            </tr>
                            <tr>
                                <th>服务商派单时间:</th>
                                <td th:text="${wbInfo.get('SERVICE_ASSIGN_DATE')}"></td>
                                <th>工程师接收人:</th>
                                <td th:text="${wbInfo.get('ENGINEER_RJ_BY')}"></td>
                                <th>工程师接收时间:</th>
                                <td th:text="${wbInfo.get('ENGINEER_RJ_DATE')}"></td>
                                <th>联系用户时间:</th>
                                <td th:text="${wbInfo.get('CONTACT_USER_TIME')}"></td>
                            </tr>
                            <tr>
                                <th>实际登门时间:</th>
                                <td th:text="${wbInfo.get('ACTUALVISIT_TIME')}"></td>
                                <th>现场确认时间:</th>
                                <td th:text="${wbInfo.get('SITE_CONFIRM_TIME')}"></td>
                                <th>现场确认GPS地址:</th>
                                <td th:text="${wbInfo.get('SITE_CONFIRM_GPS')}"></td>
                                <th>是否已进行现场确认:</th>
                                <td th:text="${wbInfo.get('IF_CONFIRM')}"></td>
                            </tr>
                            <tr>
                                <th>服务完成时间:</th>
                                <td th:text="${wbInfo.get('SERVICE_FINISH_TIME')}"></td>
                                <th>是否服务完成:</th>
                                <td th:text="${wbInfo.get('IS_SERVICE_FINISH')}"></td>
                                <th>是否未登门/电话解决服务完成:</th>
                                <td th:text="${wbInfo.get('IS_REMOTE_SERFINISH')}"></td>
                                <th>服务完成GPS地址:</th>
                                <td th:text="${wbInfo.get('SERVICE_FINISH_GPS')}"></td>
                            </tr>
                            <tr>
                                <th>退单/拒收原因:</th>
                                <td th:text="${wbInfo.get('REFUSE_REASON')}"></td>
                                <th>未完成原因编码:</th>
                                <td th:text="${wbInfo.get('UNFINISHED_REASON')}"></td>
                                <th>未完成原因描述（名称）:</th>
                                <td th:text="${wbInfo.get('UNFINISHED_REASON_DESC')}"></td>
                                <th>集中应对项目号:</th>
                                <td th:text="${wbInfo.get('CR_PROJECT_NO')}"></td>
                            </tr>
                            <tr>
                                <th>维修时长(小时):</th>
                                <td th:text="${wbInfo.get('MTAIN_HOURS')}"></td>
                                <th>第一次中间结果反馈时间:</th>
                                <td th:text="${wbInfo.get('FIRST_MIDFDBK_DATE')}"></td>
                                <th>最后一次反馈结果（内容）:</th>
                                <td th:text="${wbInfo.get('LAST_FEEDBACK_RESULT')}"></td>
                                <th>第一次结单反馈时间:</th>
                                <td th:text="${wbInfo.get('FIRST_FEEDBACK_DATE')}"></td>
                            </tr>
                            <tr>
                                <th>用户信息是否有问题:</th>
                                <td th:text="${wbInfo.get('IF_ERROR_CUST')}"></td>
                                <th>正确用户信息:</th>
                                <td th:text="${wbInfo.get('CORRECT_CUST')}"></td>
                                <th>实际服务类型:</th>
                                <td th:text="${wbInfo.get('SERVICE_TYPE')}"></td>
                                <th>实际服务方式:</th>
                                <td th:text="${wbInfo.get('SERVICE_MODE')}"></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="layui-card">
                    <div class="layui-card-header">故障树信息</div>
                    <div class="layui-card-body ">
                        <table class="layui-table"  th:each="gzslist:${data.get('GZSLIST')}">
                            <tbody>
                            <tr>
                                <th>故障树ID:</th>
                                <td th:text="${gzslist.get('FAULT_ID')}"></td>
                                <th>故障树编码:</th>
                                <td th:text="${gzslist.get('FAULT_CODE')}"></td>
                                <th>故障原因ID:</th>
                                <td th:text="${gzslist.get('FAULT_REASON_ID')}"></td>
                                <th>故障原因名称:</th>
                                <td th:text="${gzslist.get('FAULT_REASON_NAME')}"></td>
                            </tr>
                            <tr>
                                <th>故障现象ID:</th>
                                <td th:text="${gzslist.get('PHENOMENA_ID')}"></td>
                                <th>故障现象名称:</th>
                                <td th:text="${gzslist.get('PHENOMENA_NAME')}"></td>
                                <th>维修措施ID:</th>
                                <td th:text="${gzslist.get('REPAIR_METHOD_ID')}"></td>
                                <th>维修措施名称:</th>
                                <td th:text="${gzslist.get('REPAIR_METHOD_NAME')}"></td>
                            </tr>
                            <tr>
                                <th>工作事项ID:</th>
                                <td th:text="${gzslist.get('WORKM_ID')}"></td>
                                <th>工作事项名称:</th>
                                <td th:text="${gzslist.get('WORKM_NAME')}"></td>
                                <th>故障数量:</th>
                                <td th:text="${gzslist.get('FAULT_NUM')}"></td>
                                <th>维修情况描述:</th>
                                <td th:text="${gzslist.get('MTAIN__DESC')}"></td>
                            </tr>
                            <tr>
                                <th>备件分类编码:</th>
                                <td th:text="${gzslist.get('SP_TYPE')}"></td>
                                <th>备件分类名称:</th>
                                <td th:text="${gzslist.get('SP_TYPE_NAME')}"></td>
                                <th>自动审核:</th>
                                <td th:text="${gzslist.get('AUTO_CHECK')}"></td>
                                <th>附件标识:</th>
                                <td th:text="${gzslist.get('ATTACH_MARK')}"></td>
                            </tr>
                            <tr>
                                <th>参数表标识:</th>
                                <td th:text="${gzslist.get('PARAM_MARK')}"></td>
                                <th>是否有附件:</th>
                                <td th:text="${gzslist.get('HAS_ATTACH')}"></td>
                                <th>维修档案故障信息表ID:</th>
                                <td th:text="${gzslist.get('GZDA_ID')}"></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="layui-card">
                    <div class="layui-card-header">备件信息</div>
                    <div class="layui-card-body ">
                        <table class="layui-table"  th:each="bjxxlist:${data.get('BJXXLIST')}">
                            <tbody>
                            <tr>
                                <th>新件编码:</th>
                                <td th:text="${bjxxlist.get('MATERIAL_CODE')}"></td>
                                <th>新件名称:</th>
                                <td th:text="${bjxxlist.get('MATERIAL_NAME')}"></td>
                                <th>新件申请数量:</th>
                                <td th:text="${bjxxlist.get('REQUIRE_COUNT')}"></td>
                                <th>实际旧件编码(故障件编码):</th>
                                <td th:text="${bjxxlist.get('BAD_PARTCODE')}"></td>
                            </tr>
                            <tr>
                                <th>实际旧件备件名称:</th>
                                <td th:text="${bjxxlist.get('BAD_PARTNAME')}"></td>
                                <th>实际旧件数量:</th>
                                <td th:text="${bjxxlist.get('BAD_PARTNUMBER')}"></td>
                                <th>故障件类型名称:</th>
                                <td th:text="${bjxxlist.get('FAULT_COM_TYPE_NAME')}"></td>
                                <th>工单旧件状态:</th>
                                <td th:text="${bjxxlist.get('BAD_RETURN_STATUS')}"></td>
                            </tr>
                            <tr>
                                <th>工单新旧件明细表ID:</th>
                                <td th:text="${bjxxlist.get('BJXX_ID')}"></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script>layui.use(['laydate', 'form'],
    function () {
        var laydate = layui.laydate;

        //执行一个laydate实例
        laydate.render({
            elem: '#start' //指定元素
        });

        //执行一个laydate实例
        laydate.render({
            elem: '#end' //指定元素
        });
    });

/*用户-停用*/
function member_stop(obj, id) {
    layer.confirm('确认要停用吗？',
        function (index) {

            if ($(obj).attr('title') == '启用') {

                //发异步把用户状态进行更改
                $(obj).attr('title', '停用');
                $(obj).find('i').html('&#xe62f;');

                $(obj).parents("tr").find(".td-status").find('span').addClass('layui-btn-disabled').html('已停用');
                layer.msg('已停用!', {
                    icon: 5,
                    time: 1000
                });

            } else {
                $(obj).attr('title', '启用');
                $(obj).find('i').html('&#xe601;');

                $(obj).parents("tr").find(".td-status").find('span').removeClass('layui-btn-disabled').html('已启用');
                layer.msg('已启用!', {
                    icon: 5,
                    time: 1000
                });
            }

        });
}

</script>

</html>
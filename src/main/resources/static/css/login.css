/*
* @Author: <PERSON><PERSON><PERSON><PERSON>
* @Date:   2019-04-01 13:37:17
* @Last Modified by:   z<PERSON><PERSON><PERSON>
* @Last Modified time: 2019-04-01 13:37:19
*/
.login-bg{
    /*background: #eeeeee url() 0 0 no-repeat;*/
     background:url(../images/bg.png) no-repeat center;
    background-size: cover;
    overflow: hidden;
}
.login{
    margin: 120px auto 0 auto;
    min-height: 420px;
    max-width: 420px;
    padding: 40px;
    background-color: #ffffff;
    margin-left: auto;
    margin-right: auto;
    border-radius: 4px;
    /* overflow-x: hidden; */
    box-sizing: border-box;
}
.login a.logo{
    display: block;
    height: 58px;
    width: 167px;
    margin: 0 auto 30px auto;
    background-size: 167px 42px;
}
.login .message {
    margin: 10px 0 0 -58px;
    padding: 18px 10px 18px 60px;
    background: #189F92;
    position: relative;
    color: #fff;
    font-size: 16px;
}
.login #darkbannerwrap {
    background: url(../images/aiwrap.png);
    width: 18px;
    height: 10px;
    margin: 0 0 20px -58px;
    position: relative;
}

.login input[type=text],
.login input[type=file],
.login input[type=password],
.login input[type=email], select {
    border: 1px solid #DCDEE0;
    vertical-align: middle;
    border-radius: 3px;
    height: 50px;
    padding: 0px 16px;
    font-size: 14px;
    color: #555555;
    outline:none;
    width:100%;
    box-sizing: border-box;
}
.login input[type=text]:focus,
.login input[type=file]:focus,
.login input[type=password]:focus,
.login input[type=email]:focus, select:focus {
    border: 1px solid #27A9E3;
}
.login input[type=submit],
.login input[type=button]{
    display: inline-block;
    vertical-align: middle;
    padding: 12px 24px;
    margin: 0px;
    font-size: 18px;
    line-height: 24px;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    color: #ffffff;
    background-color: #189F92;
    border-radius: 3px;
    border: none;
    -webkit-appearance: none;
    outline:none;
    width:100%;
}
.login hr {
    background: #fff url() 0 0 no-repeat;
}
.login hr.hr15 {
    height: 15px;
    border: none;
    margin: 0px;
    padding: 0px;
    width: 100%;
}
.login hr.hr20 {
    height: 20px;
    border: none;
    margin: 0px;
    padding: 0px;
    width: 100%;
}